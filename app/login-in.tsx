import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    TextInput
} from 'react-native';

import { router } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';

import Toast from 'react-native-toast-message';

import { Storage } from 'expo-sqlite/kv-store';

import env from '../env';


export default function LoginInScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距

    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    const handleTopLeftBack = () => {
        router.back();
    };

    // 处理登录逻辑
    const handleLogin = async () => {
        console.log('用户名:', username);
        console.log('密码:', password);


        try {
            const response = await fetch(env.BASE_URL + '/x_mooood_user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    do_sth: 'denglu',
                    name: username,
                    password: password,
                }),
            });

            const data = await response.json();

            if (response.ok) {
                console.log('登录返回数据', data);
                console.log('登录返回数据的完整结构:', JSON.stringify(data, null, 2));

                if (data?.code === 1000) {
                    console.log('登录成功！！！')
                    console.log('后端返回的完整数据:', JSON.stringify(data, null, 2));

                    // 保存登录信息到本地存储
                    try {
                        // 从data字段中获取token和过期时间
                        const responseData = data?.data;
                        const token = responseData?.token;
                        
                        if (token) {
                            await Storage.setItem('userToken', token);
                            console.log('Token已保存:', token);
                        } else {
                            console.log('未找到token字段，data结构:', responseData);
                            throw new Error('未找到token');
                        }




                        // 使用后端返回的过期时间
                        let expireTime;
                        if (responseData?.expires_at_iso) {
                            // 优先使用ISO格式时间
                            expireTime = new Date(responseData.expires_at_iso);
                            console.log('使用后端ISO过期时间:', responseData.expires_at_iso);
                        } else if (responseData?.expires_at) {
                            // 使用时间戳
                            expireTime = new Date(responseData.expires_at);
                            console.log('使用后端时间戳过期时间:', responseData.expires_at);
                        } else {
                            // 后备方案：客户端计算（1天有效期）
                            expireTime = new Date();
                            expireTime.setDate(expireTime.getDate() + 1);
                            console.log('使用客户端计算的过期时间（后备方案）');
                        }
                        
                        await Storage.setItem('tokenExpireTime', expireTime.toISOString());
                        console.log('Token过期时间已设置:', expireTime.toISOString());
                        console.log('本地时间显示:', expireTime.toLocaleString());

                        console.log('用户信息已保存到本地存储');
                    } catch (error) {
                        console.error('保存用户信息失败:', error);
                        Toast.show({
                            type: 'error',
                            text1: '保存登录信息失败',
                        });
                        return;
                    }

                    Toast.show({
                        type: 'success',
                        text1: '登录成功',
                    });

                    // 登录成功后跳转到主页
                    router.replace('/');
                } else if (data?.code === 4001) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4002) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4003) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4004) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else {
                    console.log('注册失败:', data);
                }
            } else {
                console.log('注册失败了了了', data);
            }
        } catch (error) {
            console.error('网络错误:', error);
        }
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> 登录 </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <View style={styles.loginForm}>
                            {/* <Text style={styles.welcomeText}>欢迎回来</Text> */}

                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.input}
                                    placeholder="用户名"
                                    // placeholderTextColor="#95a5a6"
                                    value={username}
                                    onChangeText={setUsername}
                                    autoCapitalize="none"
                                />
                            </View>

                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.input}
                                    placeholder="密码"
                                    // placeholderTextColor="#95a5a6"
                                    value={password}
                                    onChangeText={setPassword}
                                    secureTextEntry
                                    autoCapitalize="none"
                                />
                            </View>

                            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
                                <Text style={styles.loginButtonText}> 登录 </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',

    },

    loginForm: {
        alignItems: 'center',
    },

    welcomeText: {
        fontSize: 32,
        fontWeight: '700',
        textAlign: 'center',
        marginBottom: 50,
        color: '#2c3e50',
        letterSpacing: 1,
    },

    inputContainer: {
        width: '100%',
        marginBottom: 24,
    },

    input: {
        height: 56,
        borderWidth: 0,
        borderRadius: 16,
        paddingHorizontal: 20,
        fontSize: 16,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',

        color: '#2c3e50',
    },

    loginButton: {
        width: '100%',
        height: 56,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 32,

        backgroundColor: 'black',
    },

    loginButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: '600',
        letterSpacing: 1,
    },
});
